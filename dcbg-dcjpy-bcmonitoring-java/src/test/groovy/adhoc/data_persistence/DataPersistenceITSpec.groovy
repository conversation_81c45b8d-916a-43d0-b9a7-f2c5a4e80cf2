package adhoc.data_persistence

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class DataPersistenceITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table
	 * Verifies service correctly stores events from both new blocks and pending transactions
	 * Expected: All events are stored with correct names, block numbers, and transaction hashes
	 */
	def "Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table"() {
		given: "Mock blockchain data with new blocks and pending transactions"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup pending events: 2 role-related events from earlier block
		def pendingEvents = ["roleGranted", "roleRevoked"]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes ABI files"
		def logMessages = logAppender.list*.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }
		assert logMessages.any {
			it.contains("ABI file processed: address=") &&
					it.contains("contract_name=") &&
					it.contains("last_modified=") &&
					it.contains("events=")
		}

		and: "All 5 events are correctly stored in DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should save block height when processing pending transactions with different block numbers
	 * Verifies service correctly saves block height when transitioning between different block numbers
	 * Expected: Block height is saved when processing transitions between different block numbers
	 */
	def "Should updates block height correctly when processing pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events from two different blocks to trigger block height save
		def mockPendingEventLogs1 = createMockPendingEventLogs(["roleGranted"], 200L, "0xabc")
		def mockPendingEventLogs2 = createMockPendingEventLogs(["roleRevoked"], 201L, "0xdef")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs)

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Block height is saved when transitioning between different block numbers"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 200L,
		"Block height 200 should be saved when transitioning from block 200 to block 201"
	}

	/**
	 * Should save block height when processing new blocks
	 * Verifies service correctly saves block height when processing new blocks
	 * Expected: Block height is saved when processing new blocks
	 */
	def "Should updates block height correctly when processing new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Block height is saved when processing new blocks"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L,
		"Block height 1002 should be saved when processing new blocks"
	}

	/**
	 * Should handles multiple events from same block correctly when processing new blocks
	 * Verifies service correctly handles multiple events from the same block
	 * Expected: All events from the same block are correctly processed and saved to DynamoDB Events table when processing new blocks
	 */
	def "Should handles multiple events from same block correctly when processing new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1000L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1000L],
			[logType: 'roleGranted', txHash: '0x456def', blockNumber: 1000L],
			[logType: 'roleRevoked', txHash: '0x789abc', blockNumber: 1000L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 1)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "All events from the same block are correctly processed and saved to DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should handles multiple events from same transaction correctly when processing pending transactions
	 * Verifies service correctly handles multiple events from the same transaction
	 * Expected: All events from the same transaction are correctly processed and saved to DynamoDB Events table when processing pending transactions
	 */
	def "Should handles multiple events from same transaction correctly when processing pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events with multiple events from the same transaction
		def pendingEvents = [
			"roleGranted",
			"roleRevoked",
			"roleAdminChanged",
			"addProviderRole",
			"addTokenByProvider"
		]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "All events from the same transaction are correctly processed and saved to DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should save events but don't save block height when processing pending transactions with same block number
	 * Verifies service correctly handles pending transactions with the same block number
	 * Expected: Events are saved but block height is not saved when processing pending transactions with the same block number
	 */
	def "Should save events but don't save block height when processing pending transactions with same block number"() {
		given: "Initial block height is 150 and all dependencies available"

		// Insert initial block height into BlockHeight table
		AdhocHelper.insertBlockHeight(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 150L)

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events with same block number
		def pendingEvents = [
			"roleGranted",
			"roleRevoked",
			"roleAdminChanged"
		]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Events are saved but block height is not saved"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 3, "Expected 3 events but found ${eventsInDb.size()}"

		and: "Block height is not saved"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 150L, "Block height should not be updated"
	}

	/**
	 * Should save events and block height when processing new blocks
	 * Verifies service correctly saves events and block height when processing new blocks
	 * Expected: Events and block height are saved when processing new blocks
	 */
	def "Should save events and block height when processing new blocks"() {
		given: "Initial block height is 150 and all dependencies available"

		// Insert initial block height into BlockHeight table
		AdhocHelper.insertBlockHeight(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 150L)

		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Events and block height are saved when processing new blocks"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 3, "Expected 3 events but found ${eventsInDb.size()}"

		and: "Block height is saved"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L, "Block height should be updated to 1002"
	}

	/**
	 * Should connection pool (max 10 connections) managed correctly, operations complete successfully
	 * Verifies DynamoDB connection pool behavior matches Go version with proper connection reuse and limits
	 * Expected: All operations complete successfully with connection pool logs showing proper management
	 */
	def "Should connection pool managed correctly, operations complete successfully"() {
		given: "Multiple events that will trigger DynamoDB operations to test connection pool"

		// Setup moderate load to test connection pool behavior
		def newBlockEvents = []
		def eventTypes = [
			'addProviderRole',
			'addTokenByProvider',
			'roleAdminChanged',
			'roleGranted',
			'roleRevoked'
		]

		// Create 15 blocks with 4 events each = 60 total events (enough to stress test max 10 connections)
		for (int blockNum = 1000; blockNum < 1015; blockNum++) {
			for (int eventIdx = 0; eventIdx < 4; eventIdx++) {
				newBlockEvents << [
					logType: eventTypes[eventIdx % eventTypes.size()],
					txHash: "0x${blockNum}${eventIdx}pool",
					blockNumber: blockNum as Long
				]
			}
		}

		def mockNotifications = createMockNewHeadsNotifications(1000L, 15)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup more pending events to trigger concurrent pool operations
		def pendingEvents = []
		for (int i = 0; i < 20; i++) {
			pendingEvents << eventTypes[i % eventTypes.size()]
		}
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 500L, "0xpool")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The blockchain monitoring service processes events using connection pool"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 30 seconds to allow processing of high volume
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 30, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes events"
		def logMessages = logAppender.list*.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }

		and: "Connection pool logs show proper connection management"
		assert logMessages.any { it.contains("Reused existing DynamoDB connection from pool") }
		assert logMessages.any { it.contains("Returned DynamoDB connection to pool") }

		and: "All events are successfully stored in DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have 60 new block events + 20 pending events = 80 total
		assert eventsInDb.size() == 80, "Expected 80 events but found ${eventsInDb.size()}"

		and: "Block height is correctly updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1014L, "Block height should be updated to 1014"
	}

	/**
	 * Should handles transactions with empty events when processing new blocks
	 * Verifies service correctly handles transactions with empty events
	 * Expected: Transaction of new block processed successfully, no events saved, block height updated
	 */
	def "Should handles transactions with empty events when processing new blocks"() {
		given: "New blocks with transactions but no events"
		// Setup new blocks with transactions that have no events (empty logs)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)

		// Setup mock Web3j to return blocks with transactions but empty event logs
		setupMockWeb3jWithEmptyEvents([1000L, 1001L, 1002L])

		setUpPendingEvent(Collections.emptyList())

		when: "The blockchain monitoring service starts and processes blocks with empty events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes blocks"
		def logMessages = logAppender.list*.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }

		and: "No events are saved to DynamoDB Events table since transactions have empty events"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 0, "Expected 0 events but found ${eventsInDb.size()}"

		and: "Block height is updated even with empty events"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1, "Block height should be updated even when transactions have empty events (Go behavior)"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L,
		"Block height should be updated to 1002 even when transactions have empty events"
	}
}
